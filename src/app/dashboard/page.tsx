"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/useAuth";
import { SessionManager } from "@/components/sessions/SessionManager";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";

export default function DashboardPage() {
  const { user, logout, sessions } = useAuth();
  const router = useRouter();
  const [showSessions, setShowSessions] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
      router.push("/login");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-[#171717] py-8 px-4">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="bg-[#071922] p-6 rounded-[30px] shadow">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-white font-mina mb-2">Dashboard</h1>
                <p className="text-white/70">Welcome back, {user?.name}!</p>
              </div>
              <button
                onClick={handleLogout}
                className="px-6 py-2 bg-red-500/20 text-red-300 rounded-lg border border-red-500/30 hover:bg-red-500/30 transition-colors font-medium"
              >
                Logout
              </button>
            </div>
          </div>

        {/* User Information */}
        <div className="bg-[#071922] p-6 rounded-[20px] shadow">
          <h2 className="text-xl font-bold text-white font-mina mb-4">Account Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <label className="text-white/70 text-sm font-medium">Name</label>
                <p className="text-white text-lg">{user?.name}</p>
              </div>
              <div>
                <label className="text-white/70 text-sm font-medium">Email</label>
                <p className="text-white text-lg">{user?.email}</p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <label className="text-white/70 text-sm font-medium">User ID</label>
                <p className="text-white text-sm font-mono bg-black/20 p-2 rounded">{user?.id}</p>
              </div>
              <div>
                <label className="text-white/70 text-sm font-medium">Member Since</label>
                <p className="text-white text-lg">{user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Session Overview */}
        <div className="bg-[#071922] p-6 rounded-[20px] shadow">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-white font-mina">Security Overview</h2>
            <button
              onClick={() => setShowSessions(!showSessions)}
              className="px-4 py-2 bg-[#DBD2CD] text-[#071922] rounded-lg font-medium hover:bg-[#C5B8B1] transition-colors"
            >
              {showSessions ? "Hide Sessions" : "Manage Sessions"}
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-[#171717] p-4 rounded-lg border border-white/10">
              <div className="text-2xl font-bold text-[#DBD2CD] mb-1">{sessions.length}</div>
              <div className="text-white/70 text-sm">Active Sessions</div>
            </div>
            <div className="bg-[#171717] p-4 rounded-lg border border-white/10">
              <div className="text-2xl font-bold text-green-400 mb-1">✓</div>
              <div className="text-white/70 text-sm">Account Secure</div>
            </div>
            <div className="bg-[#171717] p-4 rounded-lg border border-white/10">
              <div className="text-2xl font-bold text-blue-400 mb-1">🔒</div>
              <div className="text-white/70 text-sm">JWT + Session Auth</div>
            </div>
          </div>

          {showSessions && (
            <div className="mt-6">
              <SessionManager />
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="bg-[#071922] p-6 rounded-[20px] shadow">
          <h2 className="text-xl font-bold text-white font-mina mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <button
              onClick={() => setShowSessions(true)}
              className="p-4 bg-[#171717] rounded-lg border border-white/10 hover:border-[#DBD2CD]/30 transition-colors text-left"
            >
              <div className="text-2xl mb-2">🔐</div>
              <div className="text-white font-medium mb-1">Manage Sessions</div>
              <div className="text-white/70 text-sm">View and control active sessions</div>
            </button>
            
            <button
              onClick={() => window.open('http://localhost:8080/docs', '_blank')}
              className="p-4 bg-[#171717] rounded-lg border border-white/10 hover:border-[#DBD2CD]/30 transition-colors text-left"
            >
              <div className="text-2xl mb-2">📚</div>
              <div className="text-white font-medium mb-1">API Documentation</div>
              <div className="text-white/70 text-sm">View backend API docs</div>
            </button>
            
            <button
              onClick={handleLogout}
              className="p-4 bg-red-500/10 rounded-lg border border-red-500/30 hover:border-red-500/50 transition-colors text-left"
            >
              <div className="text-2xl mb-2">🚪</div>
              <div className="text-red-300 font-medium mb-1">Logout</div>
              <div className="text-red-300/70 text-sm">End all sessions and logout</div>
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-white/50 text-sm">
          <p>Session Management System - Powered by FastAPI & Next.js</p>
        </div>
      </div>
    </div>
    </ProtectedRoute>
  );
}
